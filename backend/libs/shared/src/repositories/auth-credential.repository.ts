import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { AuthProvider } from '../common/constants/constants';
import { AuthCredential } from '@prisma/client';

@Injectable()
export class AuthCredentialRepository extends BaseRepository<AuthCredential> {
  protected readonly modelName = 'authCredential';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Find auth credential by type and identifier
   */
  async findByTypeAndIdentifier(
    type: AuthProvider,
    identifier: string,
    options?: { include?: any },
  ): Promise<AuthCredential | null> {
    return this.findOne({
      where: { type, identifier },
      ...options,
    });
  }

  /**
   * Find all auth credentials for a user
   */
  async findByUserId(
    userId: string,
    options?: { include?: any },
  ): Promise<AuthCredential[]> {
    return this.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      ...options,
    });
  }

  /**
   * Find auth credential by user and provider type
   */
  async findByUserAndType(
    userId: string,
    type: AuthProvider,
    options?: { include?: any },
  ): Promise<AuthCredential | null> {
    return this.findOne({
      where: { userId, type },
      ...options,
    });
  }

  /**
   * Create auth credential with user relationship
   */
  async createWithUser(
    userId: string,
    type: AuthProvider,
    identifier: string,
    metadata?: any,
    options?: { include?: any },
  ): Promise<AuthCredential> {
    return this.create(
      {
        type,
        identifier,
        userId,
        metadata,
      } as Omit<AuthCredential, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
      options,
    );
  }

  /**
   * Update auth credential metadata
   */
  async updateMetadata(
    id: string,
    metadata: any,
    options?: { include?: any },
  ): Promise<AuthCredential> {
    return this.updateById(
      id,
      { metadata } as Partial<AuthCredential>,
      options,
    );
  }

  /**
   * Update OAuth tokens for a credential
   */
  async updateTokens(
    id: string,
    accessToken?: string,
    refreshToken?: string,
    expiresAt?: Date,
  ): Promise<AuthCredential> {
    return this.updateById(id, {
      accessToken,
      refreshToken,
      expiresAt,
    } as Partial<AuthCredential>);
  }

  /**
   * Find credentials that are expiring soon
   */
  async findExpiringSoon(
    minutesFromNow: number = 30,
  ): Promise<AuthCredential[]> {
    const expiryThreshold = new Date(Date.now() + minutesFromNow * 60 * 1000);

    return this.findMany({
      where: {
        expiresAt: {
          lte: expiryThreshold,
        },
        accessToken: {
          not: null,
        },
      },
    });
  }

  /**
   * Remove auth credential by type and user
   */
  async removeByUserAndType(
    userId: string,
    type: AuthProvider,
  ): Promise<AuthCredential> {
    const credential = await this.findByUserAndType(userId, type);
    if (!credential) {
      throw new Error(`No ${type} credential found for user ${userId}`);
    }

    return this.softDeleteById(credential.id);
  }

  /**
   * Check if user has specific provider linked
   */
  async hasProviderLinked(
    userId: string,
    type: AuthProvider,
  ): Promise<boolean> {
    const credential = await this.findByUserAndType(userId, type);
    return !!credential;
  }

  /**
   * Get user's linked providers
   */
  async getUserProviders(userId: string): Promise<AuthProvider[]> {
    const credentials = await this.findMany({
      where: { userId },
      select: { type: true },
    });

    return credentials.map((cred) => cred.type as AuthProvider);
  }

  /**
   * Find users by provider profile data (for identity merging)
   */
  async findUsersByEmail(email: string): Promise<AuthCredential[]> {
    return this.findMany({
      where: {
        OR: [
          {
            metadata: {
              path: ['email'],
              equals: email,
            },
          },
          {
            // Also check if user table has this email
            user: {
              email: email,
            },
          },
        ],
      },
      include: {
        user: true,
      },
    });
  }

  /**
   * Transaction-safe credential creation with user check
   */
  async createOrLinkCredential(
    type: AuthProvider,
    identifier: string,
    userId: string,
    metadata: any,
  ): Promise<AuthCredential> {
    return this.transaction(async (prisma) => {
      // Check if credential already exists
      const existing = await prisma.authCredential.findUnique({
        where: {
          uq_auth_credential_type_identifier: {
            type,
            identifier,
          },
        },
      });

      if (existing) {
        if (existing.userId !== userId) {
          throw new Error(`Credential already linked to different user`);
        }

        // Update metadata if it's the same user
        return prisma.authCredential.update({
          where: { id: existing.id },
          data: { metadata, updatedAt: new Date() },
        });
      }

      // Create new credential
      return prisma.authCredential.create({
        data: {
          type,
          identifier,
          userId,
          metadata,
        },
      });
    });
  }
}
