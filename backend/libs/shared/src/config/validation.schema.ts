import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  // Server Configuration
  PORT: Joi.number().default(3000),
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'staging')
    .default('development'),

  // Database Configuration
  DATABASE_URL: Joi.string().required(),

  // Redis Configuration
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_PASSWORD: Joi.string().allow('').default(''),
  REDIS_DB: Joi.number().default(0),

  // Notification Services
  ENGAGESPOT_API_KEY: Joi.string().required(),
  ENGAGESPOT_API_SECRET: Joi.string().required(),
  EN_VERIFICATION_CODE_WORKFLOW: Joi.string().required(),

  // JWT Configuration
  JWT_SECRET: Joi.string().required().min(32),
  JWT_ACCESS_TOKEN_EXPIRY: Joi.string().default('15m'),
  JWT_REFRESH_TOKEN_EXPIRY: Joi.string().default('7d'),

  // Google OAuth Configuration
  GOOGLE_CLIENT_ID: Joi.string().required(),
  GOOGLE_CLIENT_SECRET: Joi.string().required(),

  // Apple OAuth Configuration
  APPLE_CLIENT_ID: Joi.string().required(),
  APPLE_TEAM_ID: Joi.string().required(),
  APPLE_KEY_ID: Joi.string().required(),
  APPLE_PRIVATE_KEY: Joi.string().required(),

  // OTP Configuration
  OTP_LENGTH: Joi.number().min(4).max(8).default(6),
  OTP_EXPIRY_MINUTES: Joi.number().min(1).max(60).default(5),
  OTP_WINDOW: Joi.number().min(0).max(5).default(1),

  // Rate Limiting Configuration
  AUTH_RATE_LIMIT_TTL: Joi.number().default(900), // 15 minutes
  AUTH_RATE_LIMIT_MAX: Joi.number().default(5), // 5 attempts

  // AWS Configuration
  AWS_ACCESS_KEY_ID: Joi.string().required(),
  AWS_SECRET_ACCESS_KEY: Joi.string().required(),
  AWS_REGION: Joi.string().required(),
  AWS_BUCKET_NAME: Joi.string().required(),

  // Cashfree Configuration
  // CASHFREE_BASE_URL: Joi.string().uri().required(),
  CASHFREE_CLIENT_SECRET: Joi.string().required(),
  CASHFREE_CLIENT_ID: Joi.string().required(),
  CASHFREE_ENVIRONMENT: Joi.string().valid('production', 'sandbox').required(),
});
