import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { AuthRoleService } from './auth-role.service';
import { AppConfigModule } from '../../config/config.module';
import { UserRepository } from '../../repositories/user.repository';
import { AuthCredentialRepository } from '../../repositories/auth-credential.repository';
import { RefreshTokenRepository } from '../../repositories/refresh-token.repository';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import {
  RoleRepository,
  UserRoleRepository,
} from '../../repositories/role.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtRefreshGuard } from './guards/jwt-refresh.guard';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';
import { UserProfileModule } from '../user-profile/user-profile.module';

@Module({
  imports: [
    AppConfigModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.register({}), // We'll configure JWT in the service
    UserOnboardingModule,
    UserProfileModule,
  ],
  providers: [
    AuthService,
    AuthRoleService,
    UserRepository,
    AuthCredentialRepository,
    RefreshTokenRepository,
    NotificationService,
    RoleRepository,
    UserRoleRepository,
    UserProfileRepository,
    JwtStrategy,
    JwtAuthGuard,
    JwtRefreshGuard,
    RolesGuard,
    PermissionsGuard,
    PrismaService,
  ],
  exports: [
    AuthService,
    AuthRoleService,
    JwtAuthGuard,
    JwtRefreshGuard,
    RolesGuard,
    PermissionsGuard,
    PassportModule,
  ],
})
export class AuthModule {}
